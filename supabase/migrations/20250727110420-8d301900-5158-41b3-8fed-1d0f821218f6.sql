-- Create Users table with Egyptian-specific fields
CREATE TABLE public.users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone TEXT UNIQUE NOT NULL,
  name TEXT,
  national_id_verified BOOLEAN DEFAULT false,
  cash_preferred BOOLEAN DEFAULT true,
  whatsapp_number TEXT,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create Listings table for rental items
CREATE TABLE public.listings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  title_ar TEXT NOT NULL,
  title_en TEXT,
  description_ar TEXT,
  description_en TEXT,
  price_per_day INTEGER NOT NULL, -- EGP
  deposit_amount INTEGER NOT NULL, -- EGP
  category TEXT NOT NULL,
  location_lat DECIMAL(10, 8),
  location_lng DECIMAL(11, 8),
  location_address_ar TEXT,
  location_address_en TEXT,
  images TEXT[] DEFAULT '{}',
  available BOOLEAN DEFAULT true,
  verification_status TEXT DEFAULT 'pending' CHECK (verification_status IN ('pending', 'approved', 'rejected')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create Bookings table
CREATE TABLE public.bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  renter_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  item_id UUID REFERENCES public.listings(id) ON DELETE CASCADE,
  lender_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  days_count INTEGER NOT NULL,
  total_price INTEGER NOT NULL, -- EGP
  deposit_amount INTEGER NOT NULL, -- EGP
  commission_fee INTEGER NOT NULL, -- EGP (15% capped at 500)
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'active', 'completed', 'cancelled', 'dispute')),
  deposit_held BOOLEAN DEFAULT false,
  pre_rental_photos TEXT[] DEFAULT '{}',
  post_rental_photos TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create Reviews table
CREATE TABLE public.reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  reviewer_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  reviewee_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  comment_ar TEXT,
  comment_en TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.listings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users
CREATE POLICY "Users can view their own profile" ON public.users
FOR SELECT USING (auth.uid() = id::uuid);

CREATE POLICY "Users can update their own profile" ON public.users
FOR UPDATE USING (auth.uid() = id::uuid);

-- RLS Policies for listings
CREATE POLICY "Anyone can view approved listings" ON public.listings
FOR SELECT USING (verification_status = 'approved');

CREATE POLICY "Users can create their own listings" ON public.listings
FOR INSERT WITH CHECK (auth.uid() = owner_id::uuid);

CREATE POLICY "Users can update their own listings" ON public.listings
FOR UPDATE USING (auth.uid() = owner_id::uuid);

-- RLS Policies for bookings
CREATE POLICY "Users can view bookings they're involved in" ON public.bookings
FOR SELECT USING (
  auth.uid() = renter_id::uuid OR 
  auth.uid() = lender_id::uuid
);

CREATE POLICY "Users can create bookings as renter" ON public.bookings
FOR INSERT WITH CHECK (auth.uid() = renter_id::uuid);

CREATE POLICY "Users can update bookings they're involved in" ON public.bookings
FOR UPDATE USING (
  auth.uid() = renter_id::uuid OR 
  auth.uid() = lender_id::uuid
);

-- RLS Policies for reviews
CREATE POLICY "Anyone can view reviews" ON public.reviews
FOR SELECT USING (true);

CREATE POLICY "Users can create reviews for their bookings" ON public.reviews
FOR INSERT WITH CHECK (auth.uid() = reviewer_id::uuid);

-- Create indexes for performance
CREATE INDEX idx_listings_location ON public.listings(location_lat, location_lng);
CREATE INDEX idx_listings_category ON public.listings(category);
CREATE INDEX idx_listings_price ON public.listings(price_per_day);
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_bookings_dates ON public.bookings(start_date, end_date);

-- Function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON public.users
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_listings_updated_at
  BEFORE UPDATE ON public.listings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();