// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ghoodokjbqloilicdvys.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdob29kb2tqYnFsb2lsaWNkdnlzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM2MTE0MjIsImV4cCI6MjA2OTE4NzQyMn0.uFM7ge1DXfrRzGmKoE3Le2YLe6SYHJXNyTrmJoFwf6s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});