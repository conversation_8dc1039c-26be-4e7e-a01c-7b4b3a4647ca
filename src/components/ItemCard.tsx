import { MapPin, Shield, MessageCircle, Heart, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button-ar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { useNavigate } from "react-router-dom"

interface ItemCardProps {
  id: string
  title_ar: string
  description_ar: string
  price_per_day: number
  deposit_amount: number
  location_address_ar: string
  category: string
  images?: string[]
  owner?: {
    name: string
    national_id_verified: boolean
    rating?: number
  }
}

const ItemCard = ({ 
  id,
  title_ar, 
  description_ar, 
  price_per_day, 
  deposit_amount, 
  location_address_ar, 
  category,
  images = [],
  owner 
}: ItemCardProps) => {
  const navigate = useNavigate()
  
  const handleCardClick = () => {
    navigate(`/item/${id}`)
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 cursor-pointer" onClick={handleCardClick}>
      <CardContent className="p-0">
        {/* Image */}
        <div className="relative aspect-video overflow-hidden rounded-t-lg">
          {images.length > 0 ? (
            <img 
              src={images[0]} 
              alt={title_ar}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/40 flex items-center justify-center">
              <div className="text-4xl opacity-30">📷</div>
            </div>
          )}
          
          {/* Category Badge */}
          <Badge className="absolute top-3 right-3 rtl:left-3 rtl:right-auto bg-primary text-primary-foreground font-arabic">
            {category}
          </Badge>

          {/* Favorite Button */}
          <Button
            size="icon"
            variant="ghost"
            className="absolute top-3 left-3 rtl:right-3 rtl:left-auto bg-white/80 hover:bg-white text-muted-foreground hover:text-red-500"
            onClick={(e) => {
              e.stopPropagation()
              // TODO: Implement favorite functionality
            }}
          >
            <Heart className="w-4 h-4" />
          </Button>

          {/* Verified Owner Badge */}
          {owner?.national_id_verified && (
            <Badge className="absolute bottom-3 right-3 rtl:left-3 rtl:right-auto bg-trust-verified text-white font-arabic text-xs">
              <Shield className="w-3 h-3 ml-1 rtl:mr-1 rtl:ml-0" />
              موثق
            </Badge>
          )}
        </div>

        <div className="p-4">
          {/* Title */}
          <h3 className="font-semibold text-lg mb-2 font-arabic text-right rtl:text-right line-clamp-2">
            {title_ar}
          </h3>

          {/* Description */}
          <p className="text-muted-foreground text-sm mb-3 font-arabic text-right rtl:text-right line-clamp-2">
            {description_ar}
          </p>

          {/* Location */}
          <div className="flex items-center text-sm text-muted-foreground mb-3 justify-end rtl:justify-start">
            <span className="font-arabic">{location_address_ar}</span>
            <MapPin className="w-4 h-4 mr-1 rtl:ml-1 rtl:mr-0" />
          </div>

          {/* Owner Rating */}
          {owner?.rating && (
            <div className="flex items-center justify-end rtl:justify-start mb-3">
              <span className="text-sm font-medium mr-1 rtl:ml-1 rtl:mr-0">{owner.rating}</span>
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
            </div>
          )}

          {/* Pricing */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground font-arabic">السعر يومياً</span>
              <span className="text-lg font-bold text-primary font-arabic">
                {price_per_day} ج.م
              </span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground font-arabic">التأمين</span>
              <span className="font-medium font-arabic">{deposit_amount} ج.م</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              className="flex-1 font-arabic" 
              variant="rental"
              onClick={(e) => {
                e.stopPropagation()
                navigate(`/item/${id}`)
              }}
            >
              استأجر الآن
            </Button>
            <Button 
              variant="outline" 
              size="icon" 
              className="shrink-0"
              onClick={(e) => {
                e.stopPropagation()
                // TODO: Implement messaging functionality
              }}
            >
              <MessageCircle className="w-4 h-4" />
            </Button>
          </div>

          {/* Savings Badge */}
          <div className="mt-3 text-center">
            <Badge variant="secondary" className="font-arabic text-trust-verified">
              أوفر 80% من الشراء
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemCard