import { useEffect, useRef, useState } from 'react'
import { Map, View } from 'ol'
import TileLayer from 'ol/layer/Tile'
import VectorLayer from 'ol/layer/Vector'
import VectorSource from 'ol/source/Vector'
import OSM from 'ol/source/OSM'
import Feature from 'ol/Feature'
import Point from 'ol/geom/Point'
import { fromLonLat, toLonLat } from 'ol/proj'
import { Style, Icon } from 'ol/style'
import 'ol/ol.css'

interface MapViewProps {
  center?: [number, number]
  zoom?: number
  markers?: Array<{
    id: string
    position: [number, number]
    title: string
    onClick?: () => void
  }>
  height?: string
  onMapClick?: (coordinates: [number, number]) => void
}

export const MapView = ({ 
  center = [31.2357, 30.0444], // Cairo coordinates
  zoom = 10,
  markers = [],
  height = '400px',
  onMapClick
}: MapViewProps) => {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<Map | null>(null)
  const [isMapReady, setIsMapReady] = useState(false)

  useEffect(() => {
    if (!mapRef.current) return

    // Create the map
    const vectorSource = new VectorSource()
    
    const vectorLayer = new VectorLayer({
      source: vectorSource,
    })

    const map = new Map({
      target: mapRef.current,
      layers: [
        new TileLayer({
          source: new OSM(),
        }),
        vectorLayer,
      ],
      view: new View({
        center: fromLonLat(center),
        zoom: zoom,
      }),
    })

    mapInstanceRef.current = map
    setIsMapReady(true)

    // Handle map clicks
    if (onMapClick) {
      map.on('click', (event) => {
        const coordinate = map.getCoordinateFromPixel(event.pixel)
        const lonLat = toLonLat(coordinate)
        onMapClick([lonLat[0], lonLat[1]])
      })
    }

    return () => {
      map.setTarget(undefined)
      mapInstanceRef.current = null
      setIsMapReady(false)
    }
  }, [center, zoom, onMapClick])

  // Update markers when they change
  useEffect(() => {
    if (!mapInstanceRef.current || !isMapReady) return

    const vectorLayer = mapInstanceRef.current.getLayers().getArray()[1] as VectorLayer<VectorSource>
    const vectorSource = vectorLayer.getSource()
    
    if (!vectorSource) return

    // Clear existing markers
    vectorSource.clear()

    // Add new markers
    markers.forEach((marker) => {
      const feature = new Feature({
        geometry: new Point(fromLonLat(marker.position)),
        id: marker.id,
        title: marker.title,
      })

      feature.setStyle(
        new Style({
          image: new Icon({
            anchor: [0.5, 1],
            anchorXUnits: 'fraction',
            anchorYUnits: 'fraction',
            src: 'data:image/svg+xml;base64,' + btoa(`
              <svg width="32" height="40" viewBox="0 0 32 40" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 0C7.164 0 0 7.164 0 16c0 8.836 16 24 16 24s16-15.164 16-24C32 7.164 24.836 0 16 0z" fill="#e11d48"/>
                <circle cx="16" cy="16" r="8" fill="white"/>
              </svg>
            `),
          }),
        })
      )

      vectorSource.addFeature(feature)
    })

    // Handle marker clicks
    mapInstanceRef.current.on('click', (event) => {
      mapInstanceRef.current?.forEachFeatureAtPixel(event.pixel, (feature) => {
        const markerId = feature.get('id')
        const marker = markers.find(m => m.id === markerId)
        if (marker?.onClick) {
          marker.onClick()
        }
      })
    })
  }, [markers, isMapReady])

  return (
    <div 
      ref={mapRef} 
      style={{ width: '100%', height }} 
      className="rounded-lg overflow-hidden border border-border"
    />
  )
}