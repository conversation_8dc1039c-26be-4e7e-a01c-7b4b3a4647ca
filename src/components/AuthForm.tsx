import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button-ar'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useAuth } from '@/hooks/useAuth'
import { Mail, Phone, User, Lock } from 'lucide-react'

const emailSchema = z.object({
  email: z.string().email('البريد الإلكتروني غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  name: z.string().min(2, 'الاسم يجب أن يكون حرفين على الأقل').optional(),
  phone: z.string().min(10, 'رقم الهاتف غير صحيح').optional(),
})

const phoneSchema = z.object({
  phone: z.string().min(10, 'رقم الهاتف غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  name: z.string().min(2, 'الاسم يجب أن يكون حرفين على الأقل').optional(),
  email: z.string().email('البريد الإلكتروني غير صحيح').optional(),
})

interface AuthFormProps {
  onSuccess?: () => void
}

export const AuthForm = ({ onSuccess }: AuthFormProps) => {
  const [isLogin, setIsLogin] = useState(true)
  const [authMethod, setAuthMethod] = useState<'email' | 'phone'>('email')
  const { signInWithEmail, signInWithPhone, signUpWithEmail, signUpWithPhone } = useAuth()

  const schema = authMethod === 'email' ? emailSchema : phoneSchema

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      phone: '',
      password: '',
      name: '',
    },
  })

  const onSubmit = async (values: z.infer<typeof schema>) => {
    if (isLogin) {
      // Login
      if (authMethod === 'email') {
        await signInWithEmail(values.email!, values.password)
      } else {
        await signInWithPhone(values.phone!, values.password)
      }
    } else {
      // Signup
      const userData = {
        name: values.name,
        phone: authMethod === 'email' ? values.phone : values.phone,
        email: authMethod === 'phone' ? values.email : values.email,
      }
      
      if (authMethod === 'email') {
        await signUpWithEmail(values.email!, values.password, userData)
      } else {
        await signUpWithPhone(values.phone!, values.password, userData)
      }
    }
    
    onSuccess?.()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-arabic">
            {isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}
          </CardTitle>
          <CardDescription className="font-arabic">
            {isLogin ? 'أدخل بياناتك للدخول إلى حسابك' : 'أنشئ حساب جديد للبدء في الاستأجار'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={authMethod} onValueChange={(value) => setAuthMethod(value as 'email' | 'phone')}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="email" className="font-arabic">
                <Mail className="w-4 h-4 ml-2" />
                البريد الإلكتروني
              </TabsTrigger>
              <TabsTrigger value="phone" className="font-arabic">
                <Phone className="w-4 h-4 ml-2" />
                رقم الهاتف
              </TabsTrigger>
            </TabsList>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <TabsContent value="email" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            placeholder="<EMAIL>"
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="phone" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">رقم الهاتف</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="tel"
                            placeholder="+20 1xx xxxx xxx"
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {!isLogin && (
                  <>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-arabic">الاسم الكامل</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="أدخل اسمك الكامل"
                              className="text-right font-arabic"
                              dir="rtl"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {authMethod === 'email' && (
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-arabic">رقم الهاتف (اختياري)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="tel"
                                placeholder="+20 1xx xxxx xxx"
                                className="text-right"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    
                    {authMethod === 'phone' && (
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-arabic">البريد الإلكتروني (اختياري)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="email"
                                placeholder="<EMAIL>"
                                className="text-right"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </>
                )}

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-arabic">كلمة المرور</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="password"
                          placeholder="••••••••"
                          className="text-right"
                          dir="ltr"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  size="lg"
                  variant="rental"
                  className="w-full font-arabic"
                  disabled={form.formState.isSubmitting}
                >
                  {form.formState.isSubmitting ? (
                    'جاري التحميل...'
                  ) : isLogin ? (
                    'تسجيل الدخول'
                  ) : (
                    'إنشاء حساب'
                  )}
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={() => setIsLogin(!isLogin)}
                className="text-primary hover:underline font-arabic"
              >
                {isLogin ? 'ليس لديك حساب؟ أنشئ حساب جديد' : 'لديك حساب؟ سجل الدخول'}
              </button>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}