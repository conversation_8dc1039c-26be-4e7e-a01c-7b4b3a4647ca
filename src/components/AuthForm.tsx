import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button-ar'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useAuth } from '@/hooks/useAuth'
import { Mail, Phone, User, Lock } from 'lucide-react'

const createEmailSchema = (isLogin: boolean) => z.object({
  email: z.string().email('البريد الإلكتروني غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  name: isLogin ? z.string().optional() : z.string().min(2, 'الاسم يجب أن يكون حرفين على الأقل'),
  phone: z.string().optional(),
})

const createPhoneSchema = (isLogin: boolean) => z.object({
  phone: z.string().min(10, 'رقم الهاتف غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  name: isLogin ? z.string().optional() : z.string().min(2, 'الاسم يجب أن يكون حرفين على الأقل'),
  email: z.string().email('البريد الإلكتروني غير صحيح').optional().or(z.literal('')),
})

interface AuthFormProps {
  onSuccess?: () => void
}

export const AuthForm = ({ onSuccess }: AuthFormProps) => {
  const [isLogin, setIsLogin] = useState(true)
  const [authMethod, setAuthMethod] = useState<'email' | 'phone'>('email')
  const { signInWithEmail, signInWithPhone, signUpWithEmail, signUpWithPhone, checkAuthConfig } = useAuth()

  const schema = authMethod === 'email' ? createEmailSchema(isLogin) : createPhoneSchema(isLogin)

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      phone: '',
      password: '',
      name: '',
    },
  })

  // Reset form when switching between login/signup or auth methods
  React.useEffect(() => {
    form.reset({
      email: '',
      phone: '',
      password: '',
      name: '',
    })
  }, [isLogin, authMethod, form])

  const onSubmit = async (values: z.infer<typeof schema>) => {
    console.log('🔐 Form submission started:', { isLogin, authMethod, values: { ...values, password: '***' } })

    try {
      let result

      if (isLogin) {
        // Login
        console.log('🔑 Attempting login...')
        if (authMethod === 'email') {
          result = await signInWithEmail(values.email!, values.password)
        } else {
          result = await signInWithPhone(values.phone!, values.password)
        }
      } else {
        // Signup
        console.log('📝 Attempting signup...')
        const userData = {
          name: values.name,
          phone: authMethod === 'email' ? values.phone : values.phone,
          email: authMethod === 'phone' ? values.email : values.email,
        }

        console.log('👤 User data:', userData)

        if (authMethod === 'email') {
          result = await signUpWithEmail(values.email!, values.password, userData)
        } else {
          result = await signUpWithPhone(values.phone!, values.password, userData)
        }
      }

      console.log('✅ Authentication result:', result)

      // Only call onSuccess if there's no error
      if (!result?.error) {
        console.log('🎉 Authentication successful, calling onSuccess')
        onSuccess?.()
      } else {
        console.error('❌ Authentication failed:', result.error)
      }
    } catch (error) {
      console.error('💥 Unexpected error during authentication:', error)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-arabic">
            {isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}
          </CardTitle>
          <CardDescription className="font-arabic">
            {isLogin ? 'أدخل بياناتك للدخول إلى حسابك' : 'أنشئ حساب جديد للبدء في الاستأجار'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={authMethod} onValueChange={(value) => setAuthMethod(value as 'email' | 'phone')}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="email" className="font-arabic">
                <Mail className="w-4 h-4 ml-2" />
                البريد الإلكتروني
              </TabsTrigger>
              <TabsTrigger value="phone" className="font-arabic">
                <Phone className="w-4 h-4 ml-2" />
                رقم الهاتف
              </TabsTrigger>
            </TabsList>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <TabsContent value="email" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            placeholder="<EMAIL>"
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="phone" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">رقم الهاتف</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="tel"
                            placeholder="+20 1xx xxxx xxx"
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {!isLogin && (
                  <>
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="font-arabic">الاسم الكامل</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="أدخل اسمك الكامل"
                              className="text-right font-arabic"
                              dir="rtl"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    {authMethod === 'email' && (
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-arabic">رقم الهاتف (اختياري)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="tel"
                                placeholder="+20 1xx xxxx xxx"
                                className="text-right"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                    
                    {authMethod === 'phone' && (
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="font-arabic">البريد الإلكتروني (اختياري)</FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                type="email"
                                placeholder="<EMAIL>"
                                className="text-right"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </>
                )}

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-arabic">كلمة المرور</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="password"
                          placeholder="••••••••"
                          className="text-right"
                          dir="ltr"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  size="lg"
                  variant="rental"
                  className="w-full font-arabic"
                  disabled={form.formState.isSubmitting}
                >
                  {form.formState.isSubmitting ? (
                    'جاري التحميل...'
                  ) : isLogin ? (
                    'تسجيل الدخول'
                  ) : (
                    'إنشاء حساب'
                  )}
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-center space-y-4">
              <button
                type="button"
                onClick={() => setIsLogin(!isLogin)}
                className="text-primary hover:underline font-arabic"
              >
                {isLogin ? 'ليس لديك حساب؟ أنشئ حساب جديد' : 'لديك حساب؟ سجل الدخول'}
              </button>

              {/* Debug test buttons */}
              <div className="border-t pt-4 space-y-2">
                <p className="text-sm text-muted-foreground font-arabic">اختبار سريع:</p>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      console.log('🧪 Testing <NAME_EMAIL>')
                      const result = await signUpWithEmail('<EMAIL>', 'password123', { name: 'Test User' })
                      console.log('🧪 Test signup result:', result)
                    }}
                    className="font-arabic text-xs"
                  >
                    اختبار التسجيل
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      console.log('🧪 Testing <NAME_EMAIL>')
                      const result = await signInWithEmail('<EMAIL>', 'password123')
                      console.log('🧪 Test login result:', result)
                    }}
                    className="font-arabic text-xs"
                  >
                    اختبار الدخول
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      console.log('🔧 Checking auth configuration...')
                      const result = await checkAuthConfig()
                      console.log('🔧 Auth config result:', result)
                    }}
                    className="font-arabic text-xs"
                  >
                    فحص الإعدادات
                  </Button>
                </div>
              </div>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}