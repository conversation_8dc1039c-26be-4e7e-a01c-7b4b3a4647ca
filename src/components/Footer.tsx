import { MapPin, Phone, Mail, Facebook, Instagram, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button-ar"

const Footer = () => {
  const footerLinks = {
    platform: [
      { name: "كيف يعمل رينت ميت", href: "#" },
      { name: "الأمان والضمان", href: "#" },
      { name: "شروط الاستخدام", href: "#" },
      { name: "سياسة الخصوصية", href: "#" }
    ],
    support: [
      { name: "مركز المساعدة", href: "#" },
      { name: "تواصل معنا", href: "#" },
      { name: "الأسئلة الشائعة", href: "#" },
      { name: "تقرير مشكلة", href: "#" }
    ],
    categories: [
      { name: "كاميرات ومعدات تصوير", href: "#" },
      { name: "أدوات ومعدات البناء", href: "#" },
      { name: "أجهزة إلكترونية", href: "#" },
      { name: "معدات رياضية", href: "#" }
    ]
  }

  return (
    <footer className="bg-gradient-to-br from-primary to-primary-glow text-white">
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">R</span>
              </div>
              <span className="text-2xl font-bold">RentMate</span>
            </div>
            
            <p className="text-white/80 text-lg leading-relaxed font-arabic max-w-md">
              أول منصة مصرية لتأجير المعدات والأدوات بين الأفراد. 
              استأجر أي شيء تحتاجه بأقل من 10% من سعره الأصلي.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <MapPin className="w-5 h-5 text-white/60" />
                <span className="text-white/80 font-arabic">القاهرة والجيزة، مصر</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Phone className="w-5 h-5 text-white/60" />
                <span className="text-white/80 font-arabic">+20 ************</span>
              </div>
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Mail className="w-5 h-5 text-white/60" />
                <span className="text-white/80"><EMAIL></span>
              </div>
            </div>

            {/* Social Media */}
            <div className="flex space-x-4 rtl:space-x-reverse">
              <Button size="icon" variant="ghost" className="text-white hover:bg-white/20">
                <Facebook className="w-5 h-5" />
              </Button>
              <Button size="icon" variant="ghost" className="text-white hover:bg-white/20">
                <Instagram className="w-5 h-5" />
              </Button>
              <Button size="icon" variant="ghost" className="text-white hover:bg-white/20">
                <MessageCircle className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4 font-arabic">المنصة</h3>
            <ul className="space-y-2">
              {footerLinks.platform.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-white/70 hover:text-white transition-colors font-arabic text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4 font-arabic">الدعم</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-white/70 hover:text-white transition-colors font-arabic text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="font-semibold text-lg mb-4 font-arabic">الفئات</h3>
            <ul className="space-y-2">
              {footerLinks.categories.map((link, index) => (
                <li key={index}>
                  <a 
                    href={link.href} 
                    className="text-white/70 hover:text-white transition-colors font-arabic text-sm"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-white/10 backdrop-blur rounded-2xl p-6 mb-8 text-center">
          <h3 className="text-xl font-bold mb-2 font-arabic">
            ابدأ في كسب المال من أغراضك اليوم
          </h3>
          <p className="text-white/80 mb-4 font-arabic">
            ضع إعلانك في دقائق واحصل على دخل إضافي من الأشياء اللي مش بتستعملها
          </p>
          <Button variant="arab" size="lg" className="bg-white text-primary hover:bg-white/90">
            أضف إعلانك مجاناً
          </Button>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/60 text-sm font-arabic mb-4 md:mb-0">
            © 2024 RentMate. جميع الحقوق محفوظة.
          </p>
          <div className="flex space-x-6 rtl:space-x-reverse text-sm">
            <a href="#" className="text-white/60 hover:text-white font-arabic">
              شروط الخدمة
            </a>
            <a href="#" className="text-white/60 hover:text-white font-arabic">
              الخصوصية
            </a>
            <a href="#" className="text-white/60 hover:text-white font-arabic">
              ملفات تعريف الارتباط
            </a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer