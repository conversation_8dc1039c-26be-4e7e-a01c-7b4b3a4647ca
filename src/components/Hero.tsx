import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Search, Shield, Clock, MapPin, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button-ar"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import heroBg from "@/assets/hero-bg.jpg"

const Hero = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const navigate = useNavigate()
  
  const handleSearch = () => {
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`)
    } else {
      navigate('/search')
    }
  }

  return (
    <section className="relative min-h-[70vh] flex items-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <img 
          src={heroBg} 
          alt="Egyptian marketplace" 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-primary/80 via-primary/60 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center text-white">
          {/* Trust Badge */}
          <div className="flex justify-center mb-6">
            <Badge className="bg-trust-verified/90 text-white px-4 py-2 text-sm font-arabic">
              <Shield className="w-4 h-4 ml-2 rtl:mr-2 rtl:ml-0" />
              المبلغ مؤمن مع رينت ميت
            </Badge>
          </div>

          {/* Main Headline */}
          <h1 className="text-4xl md:text-6xl font-bold mb-6 font-arabic leading-tight">
            استأجر أي شيء تحتاجه
            <br />
            <span className="text-secondary">بأقل من 10% من سعره</span>
          </h1>

          <p className="text-xl md:text-2xl mb-8 font-arabic opacity-90 leading-relaxed">
            من الكاميرات للأدوات، من المعدات للأجهزة
            <br />
            اربح من الأشياء اللي مش بتستعملها
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute right-4 rtl:left-4 rtl:right-auto top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="ابحث عن كاميرا، شنيور، معدات تصوير..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="h-14 pr-12 rtl:pl-12 rtl:pr-4 text-lg text-right rtl:text-right font-arabic bg-white/95 backdrop-blur border-white/20 shadow-xl"
                dir="rtl"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button size="xl" variant="rental" className="font-arabic text-lg" onClick={handleSearch}>
              <Search className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" />
              ابحث الآن
            </Button>
            <Button size="xl" variant="arab" className="text-lg" onClick={() => navigate('/search')}>
              <MapPin className="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" />
              شوف قريب منك
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div className="bg-white/10 backdrop-blur rounded-lg p-6 border border-white/20">
              <div className="text-3xl font-bold mb-2 text-secondary">500+</div>
              <div className="text-white/90 font-arabic">مستخدم موثق</div>
            </div>
            <div className="bg-white/10 backdrop-blur rounded-lg p-6 border border-white/20">
              <div className="text-3xl font-bold mb-2 text-secondary">1000+</div>
              <div className="text-white/90 font-arabic">قطعة متاحة</div>
            </div>
            <div className="bg-white/10 backdrop-blur rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-center mb-2">
                <Star className="w-6 h-6 text-yellow-400 fill-current" />
                <span className="text-3xl font-bold ml-2 text-secondary">4.8</span>
              </div>
              <div className="text-white/90 font-arabic">تقييم المستخدمين</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero