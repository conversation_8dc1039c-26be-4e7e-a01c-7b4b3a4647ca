import { useState } from "react"
import { <PERSON>, useNavigate } from "react-router-dom"
import { Menu, X, User, Plus, Bell, LogOut } from "lucide-react"
import { Button } from "@/components/ui/button-ar"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/hooks/useAuth"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, signOut, loading, initialized } = useAuth()
  const navigate = useNavigate()

  console.log('🔍 Header - user state:', { user: user?.email, loading, initialized })

  return (
    <header className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border shadow-soft">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="text-2xl font-bold text-primary font-arabic hover:opacity-80">
              رينت ميت
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6 rtl:space-x-reverse">
            <Link to="/search">
              <Button variant="ghost" className="font-arabic">
                التصفح
              </Button>
            </Link>
            <Button variant="ghost" className="font-arabic">
              كيف يعمل
            </Button>
            <Button variant="ghost" className="font-arabic">
              الدعم
            </Button>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {user ? (
              <>
                <Link to="/list-item">
                  <Button variant="outline" size="sm" className="font-arabic">
                    <Plus className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                    أضف إعلان
                  </Button>
                </Link>
                
                <Button variant="ghost" size="sm" className="relative">
                  <Bell className="w-5 h-5" />
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-secondary">
                    3
                  </Badge>
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <User className="w-5 h-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem className="font-arabic">
                      <User className="w-4 h-4 ml-2" />
                      <Link to="/dashboard" className="w-full">
                        لوحة التحكم
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="font-arabic">
                      حجوزاتي
                    </DropdownMenuItem>
                    <DropdownMenuItem className="font-arabic">
                      إعلاناتي
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      className="font-arabic text-destructive"
                      onClick={() => signOut()}
                    >
                      <LogOut className="w-4 h-4 ml-2" />
                      تسجيل الخروج
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Link to="/auth">
                <Button variant="rental" size="sm" className="font-arabic">
                  <User className="w-4 h-4 ml-1 rtl:mr-1 rtl:ml-0" />
                  تسجيل الدخول
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-2 border-t border-border">
            <Link to="/search">
              <Button variant="ghost" className="w-full justify-start font-arabic">
                التصفح
              </Button>
            </Link>
            {!user ? (
              <Link to="/auth">
                <Button variant="rental" className="w-full font-arabic">
                  <User className="w-4 h-4 ml-2" />
                  تسجيل الدخول
                </Button>
              </Link>
            ) : (
              <>
                <Link to="/list-item">
                  <Button variant="outline" className="w-full font-arabic">
                    <Plus className="w-4 h-4 ml-2" />
                    أضف إعلان
                  </Button>
                </Link>
                <Button
                  variant="ghost"
                  onClick={() => signOut()}
                  className="w-full justify-start font-arabic text-destructive"
                >
                  <LogOut className="w-4 h-4 ml-2" />
                  تسجيل الخروج
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    </header>
  )
}

export default Header