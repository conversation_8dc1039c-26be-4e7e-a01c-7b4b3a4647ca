import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Filter } from "lucide-react"
import { Button } from "@/components/ui/button-ar"
import { Badge } from "@/components/ui/badge"
import ItemCard from "./ItemCard"
import { supabase } from "@/integrations/supabase/client"

interface Listing {
  id: string
  title_ar: string
  title_en: string
  description_ar: string
  description_en: string
  price_per_day: number
  deposit_amount: number
  category: string
  location_address_ar: string
  location_address_en: string
  images: string[]
  owner?: {
    name: string
    national_id_verified: boolean
  }
}

const FeaturedListings = () => {
  const [listings, setListings] = useState<Listing[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>("الكل")

  const categories = ["الكل", "كاميرات", "أدوات", "معدات", "إلكترونيات", "رياضة"]

  useEffect(() => {
    fetchListings()
  }, [])

  const fetchListings = async () => {
    try {
      const { data, error } = await supabase
        .from('listings')
        .select(`
          *,
          owner:users(name, national_id_verified)
        `)
        .eq('verification_status', 'approved')
        .eq('available', true)
        .limit(8)

      if (error) throw error
      setListings(data || [])
    } catch (error) {
      console.error('Error fetching listings:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredListings = selectedCategory === "الكل" 
    ? listings 
    : listings.filter(listing => listing.category === selectedCategory)

  if (loading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="h-8 bg-muted rounded w-48 mx-auto mb-4 animate-pulse" />
            <div className="h-6 bg-muted rounded w-96 mx-auto animate-pulse" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="bg-muted rounded-lg h-80 animate-pulse" />
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-8">
          <Badge className="bg-primary/10 text-primary border-primary/20 mb-4 font-arabic">
            <Filter className="w-4 h-4 ml-2 rtl:mr-2 rtl:ml-0" />
            الأكثر طلباً
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-arabic">
            اكتشف المعدات المتاحة
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-arabic">
            من الكاميرات للأدوات، كل ما تحتاجه متوفر قريب منك
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "rental" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className="font-arabic"
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Listings Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {filteredListings.map((listing) => (
            <ItemCard
              key={listing.id}
              id={listing.id}
              title_ar={listing.title_ar}
              description_ar={listing.description_ar}
              price_per_day={listing.price_per_day}
              deposit_amount={listing.deposit_amount}
              location_address_ar={listing.location_address_ar}
              category={listing.category}
              images={listing.images}
              owner={listing.owner}
            />
          ))}
        </div>

        {/* Show More Button */}
        <div className="text-center">
          <Button variant="outline" size="lg" className="font-arabic">
            عرض المزيد
            <ChevronLeft className="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" />
          </Button>
        </div>
      </div>
    </section>
  )
}

export default FeaturedListings