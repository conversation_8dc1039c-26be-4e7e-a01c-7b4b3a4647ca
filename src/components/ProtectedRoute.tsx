import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'

interface ProtectedRouteProps {
  children: React.ReactNode
  redirectTo?: string
  showHeader?: boolean
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  redirectTo = '/auth',
  showHeader = false 
}) => {
  const { user, loading, initialized } = useAuth()

  console.log('🛡️ ProtectedRoute - auth state:', { 
    user: user?.email, 
    loading, 
    initialized 
  })

  // Show loading while auth is initializing
  if (loading || !initialized) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Redirect to auth if user is not authenticated
  if (initialized && !user) {
    console.log('❌ User not authenticated, redirecting to:', redirectTo)
    return <Navigate to={redirectTo} replace />
  }

  // User is authenticated, render the protected content
  console.log('✅ User authenticated, rendering protected content')
  return <>{children}</>
}

export default ProtectedRoute
