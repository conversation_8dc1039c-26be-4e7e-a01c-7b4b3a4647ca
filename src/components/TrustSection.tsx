import { Shield, Phone, CreditCard, MapPin, Users, CheckCircle } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

const TrustSection = () => {
  const features = [
    {
      icon: <Shield className="w-8 h-8 text-trust-verified" />,
      title: "هوية موثقة",
      description: "كل مستخدم يثبت هويته برقم الهوية القومية وصورة شخصية",
      badge: "مطلوب"
    },
    {
      icon: <Phone className="w-8 h-8 text-primary" />,
      title: "رقم هاتف مؤكد",
      description: "التحقق من رقم الهاتف عبر رسالة نصية لضمان التواصل",
      badge: "آمن"
    },
    {
      icon: <CreditCard className="w-8 h-8 text-trust-secure" />,
      title: "المبلغ مؤمن",
      description: "التأمين محجوز معنا حتى إرجاع القطعة بحالة سليمة",
      badge: "مضمون"
    },
    {
      icon: <MapPin className="w-8 h-8 text-secondary" />,
      title: "مواقع حقيقية",
      description: "كل الإعلانات في أحياء حقيقية بالقاهرة والجيزة",
      badge: "موثق"
    }
  ]

  return (
    <section className="py-16 bg-gradient-to-br from-background to-muted/30">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <Badge className="bg-trust-verified/10 text-trust-verified border-trust-verified/20 mb-4 font-arabic">
            <Shield className="w-4 h-4 ml-2 rtl:mr-2 rtl:ml-0" />
            الأمان والثقة
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-arabic">
            ليه رينت ميت آمن؟
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-arabic">
            نظام حماية متكامل يضمن أمان المعاملات وحماية حقوق المستأجر والمؤجر
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card key={index} className="relative hover:shadow-lg transition-all duration-300 group border-border/50">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-gradient-sand rounded-full group-hover:scale-110 transition-transform duration-300">
                    {feature.icon}
                  </div>
                </div>
                <Badge className="mb-3 bg-muted text-muted-foreground font-arabic">
                  {feature.badge}
                </Badge>
                <h3 className="font-semibold text-lg mb-2 font-arabic">
                  {feature.title}
                </h3>
                <p className="text-sm text-muted-foreground font-arabic leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Stats */}
        <div className="bg-white/50 backdrop-blur rounded-2xl p-8 shadow-card border border-white/20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-trust-verified mr-2 rtl:ml-2 rtl:mr-0" />
                <span className="text-3xl font-bold text-trust-verified">500+</span>
              </div>
              <p className="text-muted-foreground font-arabic">مستخدم موثق الهوية</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <Shield className="w-6 h-6 text-primary mr-2 rtl:ml-2 rtl:mr-0" />
                <span className="text-3xl font-bold text-primary">100%</span>
              </div>
              <p className="text-muted-foreground font-arabic">معدل استرداد التأمين</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-center">
                <Users className="w-6 h-6 text-secondary mr-2 rtl:ml-2 rtl:mr-0" />
                <span className="text-3xl font-bold text-secondary">98%</span>
              </div>
              <p className="text-muted-foreground font-arabic">رضا المستخدمين</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TrustSection