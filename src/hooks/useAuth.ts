import { useState, useEffect } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/integrations/supabase/client'
import { toast } from 'sonner'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    console.log('🔄 Setting up auth state listener...')

    let isMounted = true

    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('🔄 Auth state changed:', { event, session: session ? 'exists' : 'null', user: session?.user?.email })

        if (!isMounted) return

        setSession(session)
        setUser(session?.user ?? null)

        // Only set loading to false after we've initialized
        if (!initialized) {
          setInitialized(true)
        }
        setLoading(false)
      }
    )

    // THEN check for existing session
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        console.log('🔍 Initial session check:', { session: session ? 'exists' : 'null', user: session?.user?.email, error })

        if (!isMounted) return

        if (error) {
          console.error('❌ Error getting initial session:', error)
        }

        setSession(session)
        setUser(session?.user ?? null)
        setInitialized(true)
        setLoading(false)
      } catch (err) {
        console.error('💥 Unexpected error during auth initialization:', err)
        if (isMounted) {
          setLoading(false)
          setInitialized(true)
        }
      }
    }

    initializeAuth()

    return () => {
      console.log('🧹 Cleaning up auth subscription')
      isMounted = false
      subscription.unsubscribe()
    }
  }, [])

  const signInWithEmail = async (email: string, password: string) => {
    console.log('🔐 signInWithEmail called:', { email })

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      console.log('🔐 signInWithEmail response:', { data, error })

      if (error) {
        console.error('❌ signInWithEmail error:', error)
        toast.error('خطأ في تسجيل الدخول', {
          description: error.message
        })
        return { error }
      }

      console.log('✅ signInWithEmail success:', data)
      toast.success('تم تسجيل الدخول بنجاح')
      return { error: null }
    } catch (err) {
      console.error('💥 signInWithEmail unexpected error:', err)
      toast.error('خطأ غير متوقع في تسجيل الدخول')
      return { error: err }
    }
  }

  const signInWithPhone = async (phone: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      phone,
      password,
    })
    
    if (error) {
      toast.error('خطأ في تسجيل الدخول', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم تسجيل الدخول بنجاح')
    return { error: null }
  }

  const signUpWithEmail = async (email: string, password: string, userData: any) => {
    console.log('📝 signUpWithEmail called:', { email, userData })

    const redirectUrl = `${window.location.origin}/`
    console.log('🔗 Redirect URL:', redirectUrl)

    try {
      // First, try to sign up with email confirmation disabled (for development)
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: userData
        }
      })

      console.log('📝 signUpWithEmail response:', { data, error })

      if (error) {
        console.error('❌ signUpWithEmail error:', error)

        // Check for specific error types
        if (error.message.includes('User already registered')) {
          toast.error('هذا البريد الإلكتروني مسجل بالفعل. جرب تسجيل الدخول بدلاً من ذلك.')
        } else if (error.message.includes('Invalid email')) {
          toast.error('البريد الإلكتروني غير صحيح')
        } else if (error.message.includes('Password')) {
          toast.error('كلمة المرور ضعيفة جداً. يجب أن تكون 6 أحرف على الأقل')
        } else {
          toast.error('خطأ في التسجيل', {
            description: error.message
          })
        }
        return { error }
      }

      console.log('✅ signUpWithEmail success:', data)

      // Check if email confirmation is required
      if (data.user && !data.session) {
        console.log('📧 Email confirmation required')
        toast.success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب.', {
          duration: 10000
        })
      } else if (data.user && data.session) {
        console.log('🎉 User signed up and logged in immediately')
        toast.success('تم إنشاء الحساب وتسجيل الدخول بنجاح!')
      } else {
        console.log('⚠️ Unexpected signup response')
        toast.success('تم إنشاء الحساب بنجاح')
      }

      return { error: null, data }
    } catch (err) {
      console.error('💥 signUpWithEmail unexpected error:', err)
      toast.error('خطأ غير متوقع في التسجيل')
      return { error: err }
    }
  }

  const signUpWithPhone = async (phone: string, password: string, userData: any) => {
    const { error } = await supabase.auth.signUp({
      phone,
      password,
      options: {
        data: userData
      }
    })
    
    if (error) {
      toast.error('خطأ في التسجيل', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم إنشاء الحساب بنجاح')
    return { error: null }
  }

  const signOut = async () => {
    console.log('🚪 signOut called')

    try {
      const { error } = await supabase.auth.signOut()

      if (error) {
        console.error('❌ signOut error:', error)
        toast.error('خطأ في تسجيل الخروج')
        return { error }
      }

      console.log('✅ signOut success')
      toast.success('تم تسجيل الخروج بنجاح')
      return { error: null }
    } catch (err) {
      console.error('💥 signOut unexpected error:', err)
      toast.error('خطأ غير متوقع في تسجيل الخروج')
      return { error: err }
    }
  }

  const checkAuthConfig = async () => {
    console.log('🔧 Checking Supabase auth configuration...')
    try {
      // Try to get the current session to see if auth is working
      const { data, error } = await supabase.auth.getSession()
      console.log('🔧 Current session:', { data, error })

      // Check if we can access the auth settings (this might not work from client)
      console.log('🔧 Supabase URL:', supabase.supabaseUrl)
      console.log('🔧 Supabase Key:', supabase.supabaseKey.substring(0, 20) + '...')

      return { data, error }
    } catch (err) {
      console.error('💥 Error checking auth config:', err)
      return { error: err }
    }
  }

  return {
    user,
    session,
    loading,
    initialized,
    signInWithEmail,
    signInWithPhone,
    signUpWithEmail,
    signUpWithPhone,
    signOut,
    checkAuthConfig,
  }
}