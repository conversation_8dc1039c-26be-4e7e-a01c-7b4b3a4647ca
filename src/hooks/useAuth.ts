import { useState, useEffect } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/integrations/supabase/client'
import { toast } from 'sonner'

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const signInWithEmail = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    if (error) {
      toast.error('خطأ في تسجيل الدخول', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم تسجيل الدخول بنجاح')
    return { error: null }
  }

  const signInWithPhone = async (phone: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      phone,
      password,
    })
    
    if (error) {
      toast.error('خطأ في تسجيل الدخول', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم تسجيل الدخول بنجاح')
    return { error: null }
  }

  const signUpWithEmail = async (email: string, password: string, userData: any) => {
    const redirectUrl = `${window.location.origin}/`
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: userData
      }
    })
    
    if (error) {
      toast.error('خطأ في التسجيل', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم إنشاء الحساب بنجاح')
    return { error: null }
  }

  const signUpWithPhone = async (phone: string, password: string, userData: any) => {
    const { error } = await supabase.auth.signUp({
      phone,
      password,
      options: {
        data: userData
      }
    })
    
    if (error) {
      toast.error('خطأ في التسجيل', {
        description: error.message
      })
      return { error }
    }
    
    toast.success('تم إنشاء الحساب بنجاح')
    return { error: null }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) {
      toast.error('خطأ في تسجيل الخروج')
      return { error }
    }
    
    toast.success('تم تسجيل الخروج بنجاح')
    return { error: null }
  }

  return {
    user,
    session,
    loading,
    signInWithEmail,
    signInWithPhone,
    signUpWithEmail,
    signUpWithPhone,
    signOut,
  }
}