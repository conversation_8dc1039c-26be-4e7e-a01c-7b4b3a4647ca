@tailwind base;
@tailwind components;
@tailwind utilities;

/* RentMate Design System - Arabic-first marketplace for Egypt
Colors inspired by Egyptian culture: Sand and Nile Blue
All colors MUST be HSL for proper theme support.
*/

@layer base {
  :root {
    /* Egyptian Sand & Nile Blue Theme */
    --background: 48 25% 97%; /* Warm sand white */
    --foreground: 210 15% 15%; /* Deep charcoal */

    --card: 48 20% 98%; /* Light sand card */
    --card-foreground: 210 15% 15%;

    --popover: 48 20% 98%;
    --popover-foreground: 210 15% 15%;

    /* Primary: Nile Blue */
    --primary: 180 100% 27%; /* Deep Nile Blue #008B8B */
    --primary-foreground: 48 25% 97%;
    --primary-glow: 180 80% 40%; /* Lighter Nile Blue for hover */

    /* Secondary: Sand tones */
    --secondary: 48 35% 88%; /* Medium sand #E1C16E */
    --secondary-foreground: 210 15% 15%;

    --muted: 48 20% 94%; /* Light sand */
    --muted-foreground: 210 10% 45%;

    --accent: 48 35% 88%; /* Sand accent */
    --accent-foreground: 210 15% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 48 25% 97%;

    --border: 48 15% 90%; /* Subtle sand border */
    --input: 48 15% 92%;
    --ring: 180 100% 27%;

    --radius: 0.75rem; /* Slightly more rounded for modern feel */

    /* Trust & Safety colors */
    --trust-verified: 142 76% 36%; /* Green for verified */
    --trust-warning: 48 96% 50%; /* Amber warning */
    --trust-secure: 180 100% 27%; /* Nile blue for security */

    /* Egyptian marketplace gradients */
    --gradient-sand: linear-gradient(135deg, hsl(48 35% 88%), hsl(48 45% 75%));
    --gradient-nile: linear-gradient(135deg, hsl(180 100% 27%), hsl(180 80% 40%));
    --gradient-hero: linear-gradient(135deg, hsl(180 100% 27%), hsl(48 35% 88%));

    /* Shadows with Egyptian warmth */
    --shadow-soft: 0 2px 8px hsl(48 20% 70% / 0.15);
    --shadow-card: 0 4px 16px hsl(48 20% 60% / 0.12);
    --shadow-trust: 0 4px 20px hsl(180 100% 27% / 0.15);

    /* Animation transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}