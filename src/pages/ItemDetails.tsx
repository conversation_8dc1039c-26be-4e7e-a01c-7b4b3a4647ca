import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { Calendar, MapPin, Shield, User, ArrowLeft, Heart, MessageCircle, Star } from 'lucide-react'
import { Button } from '@/components/ui/button-ar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { MapView } from '@/components/MapView'
import Header from '@/components/Header'
import { supabase } from '@/integrations/supabase/client'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface Listing {
  id: string
  title_ar: string
  title_en?: string
  description_ar?: string
  description_en?: string
  price_per_day: number
  deposit_amount: number
  category: string
  location_lat?: number
  location_lng?: number
  location_address_ar?: string
  location_address_en?: string
  images: string[]
  verification_status: string
  available: boolean
  created_at: string
  owner_id: string
  owner?: {
    name?: string
    national_id_verified?: boolean
  }
}

const ItemDetails = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { user } = useAuth()
  const [listing, setListing] = useState<Listing | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)

  useEffect(() => {
    if (id) {
      fetchListing()
    }
  }, [id])

  const fetchListing = async () => {
    try {
      const { data, error } = await supabase
        .from('listings')
        .select(`
          *,
          owner:users(name, national_id_verified)
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      setListing(data)
    } catch (error) {
      console.error('Error fetching listing:', error)
      toast.error('خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleBooking = () => {
    if (!user) {
      navigate('/auth')
      return
    }
    
    // TODO: Implement booking functionality
    toast.success('سيتم إضافة نظام الحجز قريباً')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!listing) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold font-arabic mb-4">الإعلان غير موجود</h1>
          <Button onClick={() => navigate(-1)} variant="outline" className="font-arabic">
            <ArrowLeft className="w-4 h-4 ml-2" />
            العودة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Button 
          onClick={() => navigate(-1)} 
          variant="ghost" 
          className="mb-6 font-arabic"
        >
          <ArrowLeft className="w-4 h-4 ml-2" />
          العودة
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Images */}
            <Card>
              <CardContent className="p-0">
                {listing.images.length > 0 ? (
                  <div className="space-y-4">
                    <div className="aspect-video relative rounded-t-lg overflow-hidden">
                      <img
                        src={listing.images[selectedImage] || '/placeholder.svg'}
                        alt={listing.title_ar}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {listing.images.length > 1 && (
                      <div className="grid grid-cols-4 gap-2 p-4">
                        {listing.images.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => setSelectedImage(index)}
                            className={`aspect-square rounded-lg overflow-hidden border-2 ${
                              selectedImage === index ? 'border-primary' : 'border-transparent'
                            }`}
                          >
                            <img
                              src={image || '/placeholder.svg'}
                              alt={`صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="aspect-video bg-muted flex items-center justify-center rounded-t-lg">
                    <span className="text-muted-foreground font-arabic">لا توجد صور</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl font-arabic">{listing.title_ar}</CardTitle>
                    {listing.title_en && (
                      <p className="text-muted-foreground mt-1">{listing.title_en}</p>
                    )}
                  </div>
                  <Badge variant="secondary" className="font-arabic">
                    {listing.category}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <MapPin className="w-4 h-4" />
                  <span className="font-arabic">{listing.location_address_ar}</span>
                </div>

                <Separator />

                <div>
                  <h3 className="font-semibold font-arabic mb-2">الوصف</h3>
                  <p className="text-muted-foreground font-arabic leading-relaxed">
                    {listing.description_ar}
                  </p>
                  {listing.description_en && (
                    <p className="text-muted-foreground mt-2 leading-relaxed">
                      {listing.description_en}
                    </p>
                  )}
                </div>

                {listing.location_lat && listing.location_lng && (
                  <div>
                    <h3 className="font-semibold font-arabic mb-2">الموقع</h3>
                    <div className="h-64 rounded-lg overflow-hidden">
                      <MapView
                        center={[listing.location_lng, listing.location_lat]}
                        zoom={15}
                        markers={[{
                          id: listing.id,
                          position: [listing.location_lng, listing.location_lat],
                          title: listing.title_ar
                        }]}
                        height="100%"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Pricing Card */}
            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">تفاصيل الإيجار</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-primary/5 rounded-lg">
                  <div className="text-3xl font-bold text-primary">
                    {listing.price_per_day} جنيه
                  </div>
                  <div className="text-muted-foreground font-arabic">لليوم الواحد</div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-arabic">مبلغ التأمين:</span>
                    <span className="font-semibold">{listing.deposit_amount} جنيه</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-arabic">حالة التوفر:</span>
                    <Badge variant={listing.available ? "default" : "secondary"} className="font-arabic">
                      {listing.available ? 'متاح' : 'غير متاح'}
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <Button 
                    onClick={handleBooking}
                    disabled={!listing.available}
                    className="w-full font-arabic"
                    size="lg"
                    variant="rental"
                  >
                    <Calendar className="w-4 h-4 ml-2" />
                    احجز الآن
                  </Button>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="font-arabic">
                      <Heart className="w-4 h-4 ml-1" />
                      أضف للمفضلة
                    </Button>
                    <Button variant="outline" className="font-arabic">
                      <MessageCircle className="w-4 h-4 ml-1" />
                      تواصل
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Owner Card */}
            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">المؤجر</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-semibold font-arabic">
                        {listing.owner?.name || 'مستخدم'}
                      </h4>
                      {listing.owner?.national_id_verified && (
                        <Badge variant="secondary" className="text-xs">
                          <Shield className="w-3 h-3 ml-1" />
                          موثق
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-4 h-4 fill-current text-yellow-400" />
                      <span className="text-sm text-muted-foreground">4.8</span>
                      <span className="text-sm text-muted-foreground font-arabic">(25 تقييم)</span>
                    </div>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full mt-4 font-arabic">
                  عرض الملف الشخصي
                </Button>
              </CardContent>
            </Card>

            {/* Safety Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="font-arabic text-sm">نصائح الأمان</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex items-start gap-2">
                  <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                  <span className="font-arabic text-muted-foreground">
                    تأكد من فحص القطعة قبل الاستلام
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                  <span className="font-arabic text-muted-foreground">
                    التقط صور للقطعة عند الاستلام والإرجاع
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                  <span className="font-arabic text-muted-foreground">
                    لا تدفع أي مبالغ خارج التطبيق
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ItemDetails