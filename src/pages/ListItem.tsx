import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button-ar'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MapView } from '@/components/MapView'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/integrations/supabase/client'
import { toast } from 'sonner'
import { Camera, MapPin, Upload } from 'lucide-react'
import Header from '@/components/Header'

const listingSchema = z.object({
  title_ar: z.string().min(5, 'العنوان يجب أن يكون 5 أحرف على الأقل'),
  title_en: z.string().min(5, 'Title must be at least 5 characters').optional(),
  description_ar: z.string().min(20, 'الوصف يجب أن يكون 20 حرف على الأقل'),
  description_en: z.string().min(20, 'Description must be at least 20 characters').optional(),
  category: z.string().min(1, 'يجب اختيار فئة'),
  price_per_day: z.number().min(1, 'السعر يجب أن يكون أكبر من صفر'),
  deposit_amount: z.number().min(0, 'التأمين يجب أن يكون صفر أو أكبر'),
  location_address_ar: z.string().min(5, 'العنوان يجب أن يكون 5 أحرف على الأقل'),
  location_address_en: z.string().optional(),
})

const categories = [
  { value: 'cameras', label_ar: 'كاميرات وتصوير', label_en: 'Cameras & Photography' },
  { value: 'tools', label_ar: 'أدوات ومعدات', label_en: 'Tools & Equipment' },
  { value: 'electronics', label_ar: 'إلكترونيات', label_en: 'Electronics' },
  { value: 'sports', label_ar: 'رياضة ولياقة', label_en: 'Sports & Fitness' },
  { value: 'vehicles', label_ar: 'مركبات', label_en: 'Vehicles' },
  { value: 'furniture', label_ar: 'أثاث ومنزل', label_en: 'Furniture & Home' },
  { value: 'other', label_ar: 'أخرى', label_en: 'Other' },
]

const ListItem = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [selectedLocation, setSelectedLocation] = useState<[number, number] | null>(null)
  const [images, setImages] = useState<File[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<z.infer<typeof listingSchema>>({
    resolver: zodResolver(listingSchema),
    defaultValues: {
      title_ar: '',
      title_en: '',
      description_ar: '',
      description_en: '',
      category: '',
      price_per_day: 0,
      deposit_amount: 0,
      location_address_ar: '',
      location_address_en: '',
    },
  })

  // No need for auth check here since ProtectedRoute handles it
  console.log('🔍 ListItem - user:', user?.email)

  const ensureUserProfile = async () => {
    if (!user) return false

    try {
      console.log('👤 Checking if user profile exists for:', user.id)

      // Check if profile exists
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        console.error('❌ Error checking profile:', fetchError)
        return false
      }

      if (profile) {
        console.log('✅ Profile exists:', profile)
        return true
      }

      // Create profile if it doesn't exist
      console.log('📝 Creating user profile...')
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          name: user.user_metadata?.name || 'مستخدم',
          phone: user.user_metadata?.phone || user.phone,
        })

      if (insertError) {
        console.error('❌ Error creating profile:', insertError)
        return false
      }

      console.log('✅ Profile created successfully')
      return true
    } catch (err) {
      console.error('💥 Unexpected error ensuring profile:', err)
      return false
    }
  }

  const onSubmit = async (values: z.infer<typeof listingSchema>) => {
    if (!user) {
      toast.error('يجب تسجيل الدخول أولاً')
      navigate('/auth')
      return
    }

    if (!selectedLocation) {
      toast.error('يرجى اختيار الموقع على الخريطة')
      return
    }

    setIsSubmitting(true)

    try {
      // Ensure user profile exists first
      const profileExists = await ensureUserProfile()
      if (!profileExists) {
        toast.error('خطأ في إعداد الملف الشخصي. حاول مرة أخرى.')
        return
      }

      // Upload images if any
      let imageUrls: string[] = []
      if (images.length > 0) {
        // TODO: Implement image upload to Supabase Storage
        // For now, we'll use placeholder URLs
        imageUrls = images.map((_, index) => `placeholder-${index}.jpg`)
      }

      console.log('📝 Creating listing with user ID:', user.id)
      console.log('📝 User metadata:', user.user_metadata)
      console.log('📝 Selected location:', selectedLocation)

      // Check current user session
      const { data: session } = await supabase.auth.getSession()
      console.log('🔐 Current session:', session)

      // Insert the listing into the database
      const { data, error } = await supabase
        .from('listings')
        .insert({
          title_ar: values.title_ar,
          title_en: values.title_en || null,
          description_ar: values.description_ar,
          description_en: values.description_en || null,
          category: values.category,
          price_per_day: values.price_per_day,
          deposit_amount: values.deposit_amount,
          location_address_ar: values.location_address_ar,
          location_address_en: values.location_address_en || null,
          owner_id: user.id,
          location_lat: selectedLocation[1],
          location_lng: selectedLocation[0],
          images: imageUrls,
          available: true,
          verification_status: 'pending'
        })
        .select()

      console.log('📝 Listing creation result:', { data, error })

      if (error) {
        console.error('Database error:', error)
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw error
      }

      toast.success('تم إضافة الإعلان بنجاح! سيتم مراجعته قريباً.')
      navigate('/')
    } catch (error: any) {
      console.error('Error creating listing:', error)
      if (error.message?.includes('row-level security') || error.message?.includes('policy')) {
        toast.error('خطأ في الأذونات. تأكد من تسجيل الدخول.')
      } else if (error.message?.includes('foreign key')) {
        toast.error('خطأ في ربط البيانات. تأكد من إعداد الملف الشخصي.')
      } else {
        toast.error('خطأ في إضافة الإعلان. حاول مرة أخرى.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // ProtectedRoute handles authentication, so we can assume user is authenticated here

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setImages(prev => [...prev, ...files].slice(0, 5)) // Max 5 images
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl font-arabic">إضافة إعلان جديد</CardTitle>
            <CardDescription className="font-arabic">
              أضف تفاصيل القطعة التي تريد تأجيرها
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title_ar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">العنوان بالعربية *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="مثال: كاميرا نيكون D850"
                            className="text-right font-arabic"
                            dir="rtl"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="title_en"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">العنوان بالإنجليزية</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="e.g: Nikon D850 Camera"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-arabic">الفئة *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="text-right font-arabic">
                            <SelectValue placeholder="اختر الفئة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              <span className="font-arabic">{category.label_ar}</span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Description */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="description_ar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">الوصف بالعربية *</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="اكتب وصف مفصل للقطعة..."
                            className="text-right font-arabic h-24"
                            dir="rtl"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description_en"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">الوصف بالإنجليزية</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Write detailed description..."
                            className="h-24"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Pricing */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="price_per_day"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">السعر لليوم (جنيه) *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            placeholder="100"
                            onChange={(e) => field.onChange(Number(e.target.value))}
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="deposit_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">مبلغ التأمين (جنيه) *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            placeholder="500"
                            onChange={(e) => field.onChange(Number(e.target.value))}
                            className="text-right"
                            dir="ltr"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Location */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="location_address_ar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="font-arabic">العنوان *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="مثال: وسط البلد، القاهرة"
                            className="text-right font-arabic"
                            dir="rtl"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <label className="text-sm font-medium font-arabic">
                      اختر الموقع على الخريطة *
                    </label>
                    <div className="mt-2">
                      <MapView
                        center={[31.2357, 30.0444]}
                        zoom={10}
                        height="300px"
                        markers={selectedLocation ? [{
                          id: 'selected',
                          position: selectedLocation,
                          title: 'الموقع المحدد'
                        }] : []}
                        onMapClick={setSelectedLocation}
                      />
                    </div>
                    {selectedLocation && (
                      <p className="text-sm text-muted-foreground mt-2 font-arabic">
                        الموقع المحدد: {selectedLocation[1].toFixed(4)}, {selectedLocation[0].toFixed(4)}
                      </p>
                    )}
                  </div>
                </div>

                {/* Images */}
                <div className="space-y-4">
                  <label className="text-sm font-medium font-arabic">
                    الصور (حتى 5 صور)
                  </label>
                  <div className="flex items-center gap-4">
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageSelect}
                      className="hidden"
                      id="image-upload"
                    />
                    <label
                      htmlFor="image-upload"
                      className="flex items-center gap-2 px-4 py-2 border border-border rounded-md cursor-pointer hover:bg-muted"
                    >
                      <Camera className="w-4 h-4" />
                      <span className="font-arabic">اختر الصور</span>
                    </label>
                    <span className="text-sm text-muted-foreground font-arabic">
                      {images.length} من 5 صور
                    </span>
                  </div>
                  
                  {images.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                      {images.map((image, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`صورة ${index + 1}`}
                            className="w-full h-20 object-cover rounded border"
                          />
                          <button
                            type="button"
                            onClick={() => setImages(prev => prev.filter((_, i) => i !== index))}
                            className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <Button
                  type="submit"
                  size="lg"
                  variant="rental"
                  className="w-full font-arabic"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'جاري النشر...' : 'نشر الإعلان'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ListItem