import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { supabase } from '@/integrations/supabase/client'
import { Button } from '@/components/ui/button-ar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Header from '@/components/Header'
import { Calendar, MapPin, Package, User, Star, TrendingUp } from 'lucide-react'

interface UserListing {
  id: string
  title_ar: string
  category: string
  price_per_day: number
  verification_status: string
  available: boolean
  created_at: string
}

interface UserBooking {
  id: string
  start_date: string
  end_date: string
  status: string
  total_price: number
  listing: {
    title_ar: string
    category: string
  }
}

const Dashboard = () => {
  const { user, signOut } = useAuth()
  const [listings, setListings] = useState<UserListing[]>([])
  const [bookings, setBookings] = useState<UserBooking[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalListings: 0,
    activeListings: 0,
    totalEarnings: 0,
    totalBookings: 0
  })

  useEffect(() => {
    console.log('🔍 Dashboard - user:', user?.email)
    if (user) {
      console.log('✅ User authenticated, fetching dashboard data')
      fetchUserData()
    }
  }, [user])

  const fetchUserData = async () => {
    if (!user) return

    try {
      // Fetch user's listings
      const { data: listingsData } = await supabase
        .from('listings')
        .select('*')
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false })

      // Fetch user's bookings as renter
      const { data: bookingsData } = await supabase
        .from('bookings')
        .select(`
          *,
          listing:listings(title_ar, category)
        `)
        .eq('renter_id', user.id)
        .order('created_at', { ascending: false })

      setListings(listingsData || [])
      setBookings(bookingsData || [])

      // Calculate stats
      const totalListings = listingsData?.length || 0
      const activeListings = listingsData?.filter(l => l.available && l.verification_status === 'approved').length || 0
      const totalEarnings = bookingsData?.reduce((sum, booking) => sum + booking.total_price, 0) || 0
      const totalBookings = bookingsData?.length || 0

      setStats({
        totalListings,
        activeListings,
        totalEarnings,
        totalBookings
      })
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoading(false)
    }
  }

  // ProtectedRoute handles authentication, so we can assume user is authenticated here

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold font-arabic mb-2">
            مرحباً، {user.user_metadata?.name || 'مستخدم'}!
          </h1>
          <p className="text-muted-foreground font-arabic">
            إدارة إعلاناتك وحجوزاتك من مكان واحد
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <Package className="w-8 h-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.totalListings}</div>
              <div className="text-sm text-muted-foreground font-arabic">إجمالي الإعلانات</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.activeListings}</div>
              <div className="text-sm text-muted-foreground font-arabic">إعلانات نشطة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Calendar className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.totalBookings}</div>
              <div className="text-sm text-muted-foreground font-arabic">إجمالي الحجوزات</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Star className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
              <div className="text-2xl font-bold">{stats.totalEarnings}</div>
              <div className="text-sm text-muted-foreground font-arabic">الأرباح (ج.م)</div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold font-arabic mb-4">إجراءات سريعة</h2>
          <div className="flex flex-wrap gap-4">
            <Link to="/list-item">
              <Button variant="rental" className="font-arabic">
                <Package className="w-4 h-4 ml-2" />
                أضف إعلان جديد
              </Button>
            </Link>
            <Link to="/search">
              <Button variant="outline" className="font-arabic">
                <MapPin className="w-4 h-4 ml-2" />
                تصفح الإعلانات
              </Button>
            </Link>
            <Button variant="outline" onClick={signOut} className="font-arabic">
              تسجيل الخروج
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* My Listings */}
          <Card>
            <CardHeader>
              <CardTitle className="font-arabic">إعلاناتي</CardTitle>
              <CardDescription className="font-arabic">
                إدارة الأشياء التي تؤجرها
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                </div>
              ) : listings.length > 0 ? (
                <div className="space-y-4">
                  {listings.slice(0, 5).map((listing) => (
                    <div key={listing.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium font-arabic">{listing.title_ar}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className="text-xs font-arabic">
                            {listing.category}
                          </Badge>
                          <Badge 
                            variant={listing.verification_status === 'approved' ? 'default' : 'secondary'}
                            className="text-xs font-arabic"
                          >
                            {listing.verification_status === 'approved' ? 'موافق عليه' : 'قيد المراجعة'}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{listing.price_per_day} ج.م</div>
                        <div className="text-xs text-muted-foreground font-arabic">يومياً</div>
                      </div>
                    </div>
                  ))}
                  {listings.length > 5 && (
                    <div className="text-center pt-2">
                      <Button variant="ghost" size="sm" className="font-arabic">
                        عرض الكل ({listings.length})
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground font-arabic mb-4">
                    لم تضف أي إعلانات بعد
                  </p>
                  <Link to="/list-item">
                    <Button variant="outline" className="font-arabic">
                      أضف إعلانك الأول
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>

          {/* My Bookings */}
          <Card>
            <CardHeader>
              <CardTitle className="font-arabic">حجوزاتي</CardTitle>
              <CardDescription className="font-arabic">
                الأشياء التي استأجرتها
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                </div>
              ) : bookings.length > 0 ? (
                <div className="space-y-4">
                  {bookings.slice(0, 5).map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium font-arabic">{booking.listing.title_ar}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className="text-xs font-arabic">
                            {booking.listing.category}
                          </Badge>
                          <Badge 
                            variant={booking.status === 'confirmed' ? 'default' : 'secondary'}
                            className="text-xs font-arabic"
                          >
                            {booking.status === 'confirmed' ? 'مؤكد' : 'قيد المراجعة'}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 font-arabic">
                          {new Date(booking.start_date).toLocaleDateString('ar-EG')} - {new Date(booking.end_date).toLocaleDateString('ar-EG')}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{booking.total_price} ج.م</div>
                        <div className="text-xs text-muted-foreground font-arabic">المجموع</div>
                      </div>
                    </div>
                  ))}
                  {bookings.length > 5 && (
                    <div className="text-center pt-2">
                      <Button variant="ghost" size="sm" className="font-arabic">
                        عرض الكل ({bookings.length})
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground font-arabic mb-4">
                    لم تقم بأي حجوزات بعد
                  </p>
                  <Link to="/search">
                    <Button variant="outline" className="font-arabic">
                      ابحث عن شيء للإيجار
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default Dashboard