import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/hooks/useAuth'
import { AuthForm } from '@/components/AuthForm'

const Auth = () => {
  const { user, loading, initialized } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    console.log('🔍 Auth page - user state:', { user: user?.email, loading, initialized })
    if (initialized && user) {
      console.log('✅ User is authenticated, redirecting to home')
      navigate('/')
    }
  }, [user, loading, initialized, navigate])

  if (loading || !initialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <AuthForm onSuccess={() => navigate('/')} />
}

export default Auth