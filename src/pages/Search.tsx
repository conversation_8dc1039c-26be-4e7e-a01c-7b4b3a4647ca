import { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import Header from '@/components/Header'
import ItemCard from '@/components/ItemCard'
import { MapView } from '@/components/MapView'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button-ar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { supabase } from '@/integrations/supabase/client'
import { Search as SearchIcon, Filter, Map, Grid3X3, MapPin } from 'lucide-react'

interface Listing {
  id: string
  title_ar: string
  description_ar?: string
  price_per_day: number
  deposit_amount: number
  location_lat?: number
  location_lng?: number
  location_address_ar?: string
  images: string[]
  category: string
  verification_status: string
}

const categories = [
  { value: 'all', label_ar: 'جميع الفئات' },
  { value: 'cameras', label_ar: 'كاميرات وتصوير' },
  { value: 'tools', label_ar: 'أدوات ومعدات' },
  { value: 'electronics', label_ar: 'إلكترونيات' },
  { value: 'sports', label_ar: 'رياضة ولياقة' },
  { value: 'vehicles', label_ar: 'مركبات' },
  { value: 'furniture', label_ar: 'أثاث ومنزل' },
  { value: 'other', label_ar: 'أخرى' },
]

const Search = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [listings, setListings] = useState<Listing[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid')
  
  // Filters
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || 'all')
  const [priceRange, setPriceRange] = useState([0, 1000])
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchListings()
  }, [])

  const fetchListings = async () => {
    try {
      let query = supabase
        .from('listings')
        .select('*')
        .eq('verification_status', 'approved')
        .eq('available', true)

      if (selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory)
      }

      if (searchQuery) {
        query = query.or(`title_ar.ilike.%${searchQuery}%,description_ar.ilike.%${searchQuery}%`)
      }

      query = query
        .gte('price_per_day', priceRange[0])
        .lte('price_per_day', priceRange[1])

      const { data, error } = await query.order('created_at', { ascending: false })

      if (error) throw error
      setListings(data || [])
    } catch (error) {
      console.error('Error fetching listings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    const params = new URLSearchParams()
    if (searchQuery) params.set('q', searchQuery)
    if (selectedCategory !== 'all') params.set('category', selectedCategory)
    setSearchParams(params)
    fetchListings()
  }

  const mapMarkers = listings
    .filter(listing => listing.location_lat && listing.location_lng)
    .map(listing => ({
      id: listing.id,
      position: [listing.location_lng!, listing.location_lat!] as [number, number],
      title: listing.title_ar,
    }))

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Search Header */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <SearchIcon className="absolute right-3 rtl:left-3 rtl:right-auto top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="ابحث عن كاميرا، شنيور، معدات تصوير..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 rtl:pl-10 rtl:pr-4 text-right font-arabic"
                dir="rtl"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} variant="rental" className="font-arabic">
              <SearchIcon className="w-4 h-4 ml-2" />
              بحث
            </Button>
          </div>

          {/* Filter and View Controls */}
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="font-arabic"
              >
                <Filter className="w-4 h-4 ml-2" />
                فلاتر
              </Button>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'map' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('map')}
                >
                  <Map className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="text-sm text-muted-foreground font-arabic">
              {listings.length} نتيجة
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="font-arabic">فلاتر البحث</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium font-arabic mb-2 block">
                      الفئة
                    </label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="text-right font-arabic">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            <span className="font-arabic">{category.label_ar}</span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium font-arabic mb-2 block">
                      السعر (جنيه/يوم)
                    </label>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        onValueChange={setPriceRange}
                        max={1000}
                        step={10}
                        className="mb-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{priceRange[0]} جنيه</span>
                        <span>{priceRange[1]} جنيه</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-end">
                    <Button onClick={fetchListings} className="w-full font-arabic">
                      تطبيق الفلاتر
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Active Filters */}
          <div className="flex flex-wrap gap-2 mt-4">
            {searchQuery && (
              <Badge variant="secondary" className="font-arabic">
                البحث: {searchQuery}
                <button
                  onClick={() => {
                    setSearchQuery('')
                    setSearchParams({})
                  }}
                  className="mr-2 text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
            {selectedCategory !== 'all' && (
              <Badge variant="secondary" className="font-arabic">
                الفئة: {categories.find(c => c.value === selectedCategory)?.label_ar}
                <button
                  onClick={() => setSelectedCategory('all')}
                  className="mr-2 text-xs"
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        </div>

        {/* Results */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {listings.map((listing) => (
              <ItemCard
                key={listing.id}
                id={listing.id}
                title_ar={listing.title_ar}
                description_ar={listing.description_ar || ''}
                price_per_day={listing.price_per_day}
                deposit_amount={listing.deposit_amount}
                location_address_ar={listing.location_address_ar || ''}
                category={listing.category}
                images={listing.images}
              />
            ))}
          </div>
        ) : (
          <div className="h-[600px]">
            <MapView
              center={[31.2357, 30.0444]}
              zoom={10}
              markers={mapMarkers}
              height="100%"
            />
          </div>
        )}

        {!loading && listings.length === 0 && (
          <div className="text-center py-12">
            <MapPin className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold font-arabic mb-2">
              لم نجد أي نتائج
            </h3>
            <p className="text-muted-foreground font-arabic">
              جرب تغيير كلمات البحث أو الفلاتر
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default Search